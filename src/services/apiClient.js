import axios from 'axios';

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5529/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Track CSRF token fetching to prevent duplicate requests
let csrfTokenPromise = null;

// Request interceptor to add auth token and CSRF token
apiClient.interceptors.request.use(
  async (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for POST, PUT, DELETE requests
    if (['post', 'put', 'delete'].includes(config.method?.toLowerCase())) {
      let csrfToken = localStorage.getItem('csrfToken');

      if (!csrfToken) {
        // Prevent multiple simultaneous CSRF token requests
        if (!csrfTokenPromise) {
          csrfTokenPromise = (async () => {
            try {
              const csrfResponse = await axios.get(`${config.baseURL}/auth/csrf-token`);
              if (csrfResponse.data.success) {
                const newCsrfToken = csrfResponse.data.data.csrfToken;
                localStorage.setItem('csrfToken', newCsrfToken);
                return newCsrfToken;
              }
              return null;
            } catch (csrfError) {
              console.warn('Failed to get CSRF token:', csrfError);
              return null;
            } finally {
              csrfTokenPromise = null;
            }
          })();
        }

        csrfToken = await csrfTokenPromise;
      }

      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('csrfToken');
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
